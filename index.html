<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯视频VIP桌面端界面原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
        }
        .desktop-container {
            min-width: 1200px;
            margin: 0 auto;
            padding: 40px;
        }
        .prototype-frame {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            margin: 30px 0;
            transition: all 0.4s ease;
        }
        .prototype-frame:hover {
            transform: translateY(-5px);
            box-shadow: 0 35px 70px rgba(0,0,0,0.2);
        }
        iframe {
            border: none;
            width: 100%;
            height: 800px;
        }
        .prototype-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        .main-title {
            text-align: center;
            margin-bottom: 50px;
        }
        .main-title h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 16px;
        }
        .desktop-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: 40px;
            max-width: 1400px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="desktop-container">
        <div class="main-title">
            <h1>腾讯视频VIP桌面端界面设计</h1>
            <p class="text-white/90 text-xl">专为PC端优化的高保真原型展示</p>
        </div>

        <div class="desktop-layout">
            <!-- 桌面端VIP主界面 -->
            <div class="prototype-frame">
                <div class="prototype-title">
                    <i class="fas fa-desktop mr-3"></i>
                    桌面端VIP主界面 - 宽屏布局优化
                </div>
                <iframe src="desktop-vip-main.html"></iframe>
            </div>
        </div>
    </div>
</body>
</html>
