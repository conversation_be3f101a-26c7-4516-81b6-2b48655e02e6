<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯视频VIP主界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .vip-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            max-width: 380px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }
        .vip-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        }
        .vip-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .benefit-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        .benefit-item:last-child {
            border-bottom: none;
        }
        .benefit-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 18px;
        }
        .cta-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 24px;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
        }
        .secondary-link {
            text-align: center;
            margin-top: 16px;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        .secondary-link:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="vip-card">
        <!-- VIP状态标识 -->
        <div class="text-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-2">智宇</h2>
            <div class="vip-badge">
                <i class="fas fa-crown mr-2"></i>
                您的腾讯视频VIP已过期
            </div>
        </div>

        <!-- VIP专享特权标题 -->
        <div class="text-center mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">VIP专享特权</h3>
        </div>

        <!-- 权益列表 -->
        <div class="space-y-2">
            <div class="benefit-item">
                <div class="benefit-icon bg-yellow-100 text-yellow-600">
                    <i class="fas fa-gem"></i>
                </div>
                <div class="flex-1">
                    <div class="font-semibold text-gray-800">尊贵身份</div>
                    <div class="text-sm text-gray-500">专属VIP标识</div>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-video"></i>
                </div>
                <div class="flex-1">
                    <div class="font-semibold text-gray-800">1080P画质</div>
                    <div class="text-sm text-gray-500">超清观影体验</div>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon bg-purple-100 text-purple-600">
                    <i class="fas fa-film"></i>
                </div>
                <div class="flex-1">
                    <div class="font-semibold text-gray-800">院线新片</div>
                    <div class="text-sm text-gray-500">抢先观看热门大片</div>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon bg-green-100 text-green-600">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="flex-1">
                    <div class="font-semibold text-gray-800">观影券</div>
                    <div class="text-sm text-gray-500">每月专属观影福利</div>
                </div>
            </div>
        </div>

        <!-- 主要行动按钮 -->
        <button class="cta-button">
            <i class="fas fa-crown mr-2"></i>
            开通腾讯视频VIP
        </button>

        <!-- 次要链接 -->
        <div class="secondary-link">
            <i class="fas fa-chevron-right mr-1"></i>
            升级腾讯视频VIP享TV等特权
        </div>
    </div>
</body>
</html>
