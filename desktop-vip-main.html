<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯视频VIP桌面端主界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        .desktop-layout {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px;
            min-height: 100vh;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        .vip-sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            height: fit-content;
            position: sticky;
            top: 40px;
        }
        .vip-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            border-radius: 24px 24px 0 0;
        }
        .user-info {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 32px;
            position: relative;
        }
        .vip-crown {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ffd700;
            color: #333;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
        }
        .vip-status {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            margin-top: 12px;
        }
        .benefits-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 32px;
        }
        .benefit-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        .benefit-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .benefit-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }
        .cta-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 18px 32px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 16px;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }
        .secondary-link {
            text-align: center;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .secondary-link:hover {
            color: #333;
        }
        .content-section {
            margin-bottom: 40px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
        }
        .section-title i {
            margin-right: 12px;
            color: #667eea;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }
        .feature-card.premium { border-left-color: #ff6b6b; }
        .feature-card.quality { border-left-color: #4ecdc4; }
        .feature-card.content { border-left-color: #45b7d1; }
        .feature-card.exclusive { border-left-color: #f9ca24; }
        .comparison-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: 600;
            font-size: 18px;
        }
        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            border-bottom: 1px solid #f0f0f0;
        }
        .table-cell {
            padding: 16px 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .table-cell.feature {
            text-align: left;
            font-weight: 500;
            justify-content: flex-start;
        }
        .check-icon { color: #27ae60; font-size: 18px; }
        .cross-icon { color: #e74c3c; font-size: 18px; }
    </style>
</head>
<body>
    <div class="desktop-layout">
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- VIP权益详情 -->
            <div class="content-section">
                <h2 class="section-title">
                    <i class="fas fa-crown"></i>
                    VIP专享权益
                </h2>
                <div class="feature-grid">
                    <div class="feature-card premium">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-100 text-red-600 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-gem text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-gray-800">尊贵身份</h3>
                                <p class="text-sm text-gray-500">专属VIP标识与特权</p>
                            </div>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>专属VIP头像框</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>优先客服支持</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>会员专属活动</li>
                        </ul>
                    </div>

                    <div class="feature-card quality">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-teal-100 text-teal-600 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-hd-video text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-gray-800">超清画质</h3>
                                <p class="text-sm text-gray-500">1080P高清观影体验</p>
                            </div>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>1080P超清画质</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>杜比音效支持</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>无广告播放</li>
                        </ul>
                    </div>

                    <div class="feature-card content">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 text-blue-600 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-film text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-gray-800">独家内容</h3>
                                <p class="text-sm text-gray-500">院线新片抢先观看</p>
                            </div>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>院线同步大片</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>VIP专享剧集</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>提前观看权限</li>
                        </ul>
                    </div>

                    <div class="feature-card exclusive">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-yellow-100 text-yellow-600 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-gift text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-gray-800">专属福利</h3>
                                <p class="text-sm text-gray-500">每月观影券赠送</p>
                            </div>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>每月10张观影券</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>会员积分翻倍</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>生日专属礼品</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 功能对比表格 -->
            <div class="content-section">
                <h2 class="section-title">
                    <i class="fas fa-balance-scale"></i>
                    功能对比
                </h2>
                <div class="comparison-table">
                    <div class="table-header">
                        VIP vs 普通用户权益对比
                    </div>
                    <div class="table-row bg-gray-50">
                        <div class="table-cell feature font-semibold">功能特权</div>
                        <div class="table-cell font-semibold">普通用户</div>
                        <div class="table-cell font-semibold">VIP用户</div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell feature">广告跳过</div>
                        <div class="table-cell"><i class="fas fa-times cross-icon"></i></div>
                        <div class="table-cell"><i class="fas fa-check check-icon"></i></div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell feature">1080P超清画质</div>
                        <div class="table-cell"><i class="fas fa-times cross-icon"></i></div>
                        <div class="table-cell"><i class="fas fa-check check-icon"></i></div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell feature">院线新片</div>
                        <div class="table-cell"><i class="fas fa-times cross-icon"></i></div>
                        <div class="table-cell"><i class="fas fa-check check-icon"></i></div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell feature">离线下载</div>
                        <div class="table-cell">限制5部</div>
                        <div class="table-cell">无限制</div>
                    </div>
                    <div class="table-row">
                        <div class="table-cell feature">观影券</div>
                        <div class="table-cell">无</div>
                        <div class="table-cell">每月10张</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP侧边栏 -->
        <div class="vip-sidebar">
            <!-- 用户信息 -->
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                    <div class="vip-crown">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">智宇</h3>
                <div class="vip-status">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    您的腾讯视频VIP已过期
                </div>
            </div>

            <!-- VIP专享特权 -->
            <div class="mb-8">
                <h4 class="text-lg font-semibold text-gray-700 mb-4 text-center">VIP专享特权</h4>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon bg-yellow-100 text-yellow-600">
                            <i class="fas fa-gem"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-sm text-gray-800">尊贵身份</div>
                            <div class="text-xs text-gray-500">专属标识</div>
                        </div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon bg-blue-100 text-blue-600">
                            <i class="fas fa-video"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-sm text-gray-800">1080P画质</div>
                            <div class="text-xs text-gray-500">超清体验</div>
                        </div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon bg-purple-100 text-purple-600">
                            <i class="fas fa-film"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-sm text-gray-800">院线新片</div>
                            <div class="text-xs text-gray-500">抢先观看</div>
                        </div>
                    </div>

                    <div class="benefit-item">
                        <div class="benefit-icon bg-green-100 text-green-600">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-sm text-gray-800">观影券</div>
                            <div class="text-xs text-gray-500">每月福利</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行动按钮 -->
            <button class="cta-button">
                <i class="fas fa-crown mr-2"></i>
                开通腾讯视频VIP
            </button>

            <div class="secondary-link">
                <i class="fas fa-chevron-right mr-2"></i>
                升级腾讯视频VIP享TV等特权
            </div>
        </div>
    </div>
</body>
</html>
