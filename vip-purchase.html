<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP购买页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .purchase-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            max-width: 400px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }
        .plan-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
        }
        .plan-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        .plan-card.selected {
            border-color: #3498db;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
        }
        .plan-card.recommended {
            border-color: #e74c3c;
        }
        .plan-card.recommended::before {
            content: '推荐';
            position: absolute;
            top: -8px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .plan-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .plan-price {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .plan-original-price {
            font-size: 14px;
            color: #95a5a6;
            text-decoration: line-through;
            margin-left: 8px;
        }
        .plan-duration {
            font-size: 16px;
            font-weight: 600;
            color: #34495e;
        }
        .plan-features {
            list-style: none;
            padding: 0;
            margin: 12px 0;
        }
        .plan-features li {
            padding: 4px 0;
            font-size: 14px;
            color: #7f8c8d;
        }
        .plan-features li::before {
            content: '✓';
            color: #27ae60;
            font-weight: bold;
            margin-right: 8px;
        }
        .payment-methods {
            margin-top: 24px;
        }
        .payment-method {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #ecf0f1;
            border-radius: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            background: #f8f9fa;
        }
        .payment-method.selected {
            border-color: #3498db;
            background: #f8f9ff;
        }
        .purchase-button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 24px;
        }
        .purchase-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.4);
        }
        .discount-badge {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="purchase-container">
        <div class="text-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-2">选择VIP套餐</h2>
            <p class="text-gray-600">享受更多专属权益</p>
        </div>

        <!-- 套餐选择 -->
        <div class="space-y-4">
            <!-- 月度套餐 -->
            <div class="plan-card" onclick="selectPlan(this)">
                <div class="plan-header">
                    <div class="plan-duration">月度VIP</div>
                    <div>
                        <span class="plan-price">¥19</span>
                        <span class="plan-original-price">¥25</span>
                    </div>
                </div>
                <ul class="plan-features">
                    <li>无广告观影</li>
                    <li>1080P超清画质</li>
                    <li>院线新片抢先看</li>
                </ul>
            </div>

            <!-- 季度套餐 -->
            <div class="plan-card recommended selected" onclick="selectPlan(this)">
                <div class="plan-header">
                    <div class="plan-duration">季度VIP</div>
                    <div>
                        <span class="plan-price">¥45</span>
                        <span class="plan-original-price">¥75</span>
                        <span class="discount-badge">省30元</span>
                    </div>
                </div>
                <ul class="plan-features">
                    <li>无广告观影</li>
                    <li>1080P超清画质</li>
                    <li>院线新片抢先看</li>
                    <li>每月5张观影券</li>
                </ul>
            </div>

            <!-- 年度套餐 -->
            <div class="plan-card" onclick="selectPlan(this)">
                <div class="plan-header">
                    <div class="plan-duration">年度VIP</div>
                    <div>
                        <span class="plan-price">¥128</span>
                        <span class="plan-original-price">¥300</span>
                        <span class="discount-badge">省172元</span>
                    </div>
                </div>
                <ul class="plan-features">
                    <li>无广告观影</li>
                    <li>1080P超清画质</li>
                    <li>院线新片抢先看</li>
                    <li>每月10张观影券</li>
                    <li>专属客服支持</li>
                </ul>
            </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-methods">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">支付方式</h3>
            
            <div class="payment-method selected" onclick="selectPayment(this)">
                <i class="fab fa-weixin text-green-500 text-xl mr-3"></i>
                <span class="flex-1">微信支付</span>
                <i class="fas fa-check-circle text-blue-500"></i>
            </div>

            <div class="payment-method" onclick="selectPayment(this)">
                <i class="fab fa-alipay text-blue-500 text-xl mr-3"></i>
                <span class="flex-1">支付宝</span>
            </div>

            <div class="payment-method" onclick="selectPayment(this)">
                <i class="fas fa-credit-card text-gray-500 text-xl mr-3"></i>
                <span class="flex-1">银行卡支付</span>
            </div>
        </div>

        <!-- 购买按钮 -->
        <button class="purchase-button">
            <i class="fas fa-lock mr-2"></i>
            立即开通VIP - ¥45
        </button>

        <!-- 服务条款 -->
        <div class="text-center mt-4">
            <p class="text-xs text-gray-500">
                点击购买即表示同意 
                <span class="text-blue-500 cursor-pointer">《服务协议》</span>
                和
                <span class="text-blue-500 cursor-pointer">《隐私政策》</span>
            </p>
        </div>
    </div>

    <script>
        function selectPlan(element) {
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });
            element.classList.add('selected');
            
            // 更新购买按钮价格
            const price = element.querySelector('.plan-price').textContent;
            document.querySelector('.purchase-button').innerHTML = 
                `<i class="fas fa-lock mr-2"></i>立即开通VIP - ${price}`;
        }

        function selectPayment(element) {
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('selected');
                method.querySelector('.fa-check-circle')?.remove();
            });
            element.classList.add('selected');
            element.innerHTML += '<i class="fas fa-check-circle text-blue-500"></i>';
        }
    </script>
</body>
</html>
