<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .user-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            max-width: 400px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }
        .user-profile {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 32px;
            position: relative;
        }
        .vip-crown {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ffd700;
            color: #333;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .user-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .user-status {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: white;
            border-radius: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .menu-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 18px;
        }
        .menu-text {
            flex: 1;
        }
        .menu-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }
        .menu-subtitle {
            font-size: 12px;
            color: #999;
        }
        .menu-arrow {
            color: #ccc;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 24px;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        .action-button.secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }
    </style>
</head>
<body>
    <div class="user-container">
        <!-- 用户资料 -->
        <div class="user-profile">
            <div class="avatar">
                <i class="fas fa-user"></i>
                <div class="vip-crown">
                    <i class="fas fa-crown"></i>
                </div>
            </div>
            <div class="user-name">智宇</div>
            <div class="user-status">VIP会员已过期</div>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">观看时长(小时)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">23</div>
                <div class="stat-label">收藏影片</div>
            </div>
        </div>

        <!-- 菜单列表 -->
        <div class="space-y-2">
            <div class="menu-item">
                <div class="menu-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="menu-text">
                    <div class="menu-title">我的VIP</div>
                    <div class="menu-subtitle">查看会员权益和续费</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>

            <div class="menu-item">
                <div class="menu-icon bg-green-100 text-green-600">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="menu-text">
                    <div class="menu-title">观影券</div>
                    <div class="menu-subtitle">5张可用</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>

            <div class="menu-item">
                <div class="menu-icon bg-purple-100 text-purple-600">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="menu-text">
                    <div class="menu-title">我的收藏</div>
                    <div class="menu-subtitle">23部影片</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>

            <div class="menu-item">
                <div class="menu-icon bg-orange-100 text-orange-600">
                    <i class="fas fa-history"></i>
                </div>
                <div class="menu-text">
                    <div class="menu-title">观看历史</div>
                    <div class="menu-subtitle">最近观看的内容</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>

            <div class="menu-item">
                <div class="menu-icon bg-red-100 text-red-600">
                    <i class="fas fa-download"></i>
                </div>
                <div class="menu-text">
                    <div class="menu-title">离线缓存</div>
                    <div class="menu-subtitle">管理下载内容</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>

            <div class="menu-item">
                <div class="menu-icon bg-gray-100 text-gray-600">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="menu-text">
                    <div class="menu-title">设置</div>
                    <div class="menu-subtitle">账号与隐私设置</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <button class="action-button">
                <i class="fas fa-crown mr-2"></i>
                续费VIP
            </button>
            <button class="action-button secondary">
                <i class="fas fa-headset mr-2"></i>
                客服
            </button>
        </div>
    </div>
</body>
</html>
